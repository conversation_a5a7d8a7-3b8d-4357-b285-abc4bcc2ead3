{"name": "expo-splash-screen", "version": "0.30.8", "description": "Provides a module to allow keeping the native Splash Screen visible until you choose to hide it.", "main": "build", "types": "build", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-splash-screen", "splash-screen", "splash", "launch-screen", "launch"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-splash-screen"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/splash-screen/", "dependencies": {"@expo/prebuild-config": "^9.0.5"}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "7cd543b5d7b836fed6dca00859e35883c490b9ef"}