{"name": "expo-font", "version": "13.3.1", "description": "Load fonts at runtime and use them in React Native components.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "build:plugin": "expo-module build plugin", "test:plugin": "expo-module test plugin", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "exports": {"./app.plugin": "./app.plugin.js", "./app.plugin.js": "./app.plugin.js", "./package.json": "./package.json", ".": {"react-server": "./build/index.server.js", "default": "./build/index.js"}, "./build/server": "./build/server.js"}, "keywords": ["react-native", "expo", "font"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-font"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/font/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"fontfaceobserver": "^2.1.0"}, "devDependencies": {"@testing-library/react-native": "^13.1.0", "@types/fontfaceobserver": "^2.1.3", "expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react": "*"}, "gitHead": "5c98e5ee7c577209319bf91f515e65e256641d9c"}